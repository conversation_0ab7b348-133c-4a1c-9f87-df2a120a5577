import type { Route } from "./+types/evenimente";
import { Layout } from "../components/Layout";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Evenimente - Educația Holistică" },
    { name: "description", content: "Descoperă evenimentele, workshop-urile și conferințele organizate de Asociația Educația Holistică." },
  ];
}

export default function Evenimente() {
  const evenimente = [
    {
      id: 1,
      title: "Workshop: Mindfulness pentru Copii",
      date: "15 Martie 2024",
      time: "10:00 - 12:00",
      location: "Centrul Educația Holistică, București",
      description: "Un workshop interactiv pentru părinți și copii (6-12 ani) despre tehnicile de mindfulness adaptate vârstei.",
      image: "https://via.placeholder.com/400x250/DBEAFE/3B82F6?text=Mindfulness+Copii",
      category: "Workshop",
      price: "Gratuit"
    },
    {
      id: 2,
      title: "Conferința Anuală: Viitorul Educației",
      date: "22 Aprilie 2024",
      time: "09:00 - 17:00",
      location: "Sala Palatului, București",
      description: "O conferință dedicată educatorilor și părinților despre tendințele moderne în educația holistică.",
      image: "https://via.placeholder.com/400x250/F3E8FF/8B5CF6?text=Conferinta+Educatie",
      category: "Conferință",
      price: "150 RON"
    },
    {
      id: 3,
      title: "Curs de Formare: Educația Emoțională",
      date: "5-7 Mai 2024",
      time: "09:00 - 16:00",
      location: "Online & Prezențial",
      description: "Un curs intensiv de 3 zile pentru educatori despre implementarea educației emoționale în curriculum.",
      image: "https://via.placeholder.com/400x250/ECFDF5/10B981?text=Educatie+Emotionala",
      category: "Curs",
      price: "450 RON"
    },
    {
      id: 4,
      title: "Atelier de Artă Terapie pentru Adulți",
      date: "18 Mai 2024",
      time: "14:00 - 17:00",
      location: "Atelier Creativ, București",
      description: "Descoperă puterea vindecătoare a artei într-un atelier ghidat de terapeuti specializați.",
      image: "https://via.placeholder.com/400x250/FEF3C7/F59E0B?text=Arte+Terapie",
      category: "Atelier",
      price: "120 RON"
    },
    {
      id: 5,
      title: "Retreat de Weekend: Echilibru și Armonie",
      date: "1-2 Iunie 2024",
      time: "Vineri 18:00 - Duminică 16:00",
      location: "Casa de Oaspeți, Brașov",
      description: "Un weekend de reconectare cu sine prin meditație, yoga și activități în natură.",
      image: "https://via.placeholder.com/400x250/E0F2FE/0284C7?text=Retreat+Weekend",
      category: "Retreat",
      price: "380 RON"
    },
    {
      id: 6,
      title: "Webinar: Comunicarea Nonviolentă în Familie",
      date: "20 Iunie 2024",
      time: "19:00 - 21:00",
      location: "Online",
      description: "Învață tehnicile de comunicare nonviolentă pentru relații familiale mai armonioase.",
      image: "https://via.placeholder.com/400x250/FDF2F8/EC4899?text=Comunicare+Familie",
      category: "Webinar",
      price: "Gratuit"
    }
  ];

  const categorii = ["Toate", "Workshop", "Conferință", "Curs", "Atelier", "Retreat", "Webinar"];

  return (
    <Layout>
      {/* Hero Section */}
      <section className="py-20" style={{ background: 'linear-gradient(135deg, #fef7f0 0%, #f9f6eb 50%, #fdeee0 100%)' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6 font-playfair">
              Evenimente & <span style={{ color: '#b04b0f' }}>Workshop-uri</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Participă la evenimentele noastre educaționale și conectează-te cu o comunitate 
              pasionată de dezvoltarea holistică.
            </p>
          </div>
        </div>
      </section>

      {/* Filter Section */}
      <section className="py-8 bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap justify-center gap-4">
            {categorii.map((categoria) => (
              <button
                key={categoria}
                className="px-6 py-2 rounded-full border border-gray-300 text-gray-700 transition-colors duration-200"
                style={{
                  ':hover': {
                    backgroundColor: '#fef7f0',
                    borderColor: '#b04b0f',
                    color: '#b04b0f'
                  }
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#fef7f0';
                  e.currentTarget.style.borderColor = '#b04b0f';
                  e.currentTarget.style.color = '#b04b0f';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                  e.currentTarget.style.borderColor = '#d1d5db';
                  e.currentTarget.style.color = '#374151';
                }}
              >
                {categoria}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Events Grid */}
      <section className="py-20" style={{ backgroundColor: '#f9f6eb' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {evenimente.map((eveniment) => (
              <div key={eveniment.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <div className="relative">
                  <img
                    src={eveniment.image}
                    alt={eveniment.title}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="text-white px-3 py-1 rounded-full text-sm font-semibold" style={{ backgroundColor: '#b04b0f' }}>
                      {eveniment.category}
                    </span>
                  </div>
                  <div className="absolute top-4 right-4">
                    <span className="bg-white text-gray-900 px-3 py-1 rounded-full text-sm font-semibold">
                      {eveniment.price}
                    </span>
                  </div>
                </div>
                
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    {eveniment.title}
                  </h3>
                  
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center text-gray-600">
                      <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <span className="text-sm">{eveniment.date}</span>
                    </div>
                    
                    <div className="flex items-center text-gray-600">
                      <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span className="text-sm">{eveniment.time}</span>
                    </div>
                    
                    <div className="flex items-center text-gray-600">
                      <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      <span className="text-sm">{eveniment.location}</span>
                    </div>
                  </div>
                  
                  <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                    {eveniment.description}
                  </p>
                  
                  <button
                    className="w-full text-white py-2 px-4 rounded-lg font-semibold transition-colors duration-200"
                    style={{ backgroundColor: '#b04b0f' }}
                    onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#a0420e'}
                    onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#b04b0f'}
                    Înscrie-te
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-20" style={{ backgroundColor: '#b04b0f' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4 font-playfair">
            Fii la Curent cu Evenimentele Noastre
          </h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto" style={{ color: '#fef7f0' }}>
            Abonează-te la newsletter-ul nostru pentru a primi notificări despre 
            evenimentele viitoare și oportunitățile de dezvoltare.
          </p>
          <div className="max-w-md mx-auto flex gap-4">
            <input
              type="email"
              placeholder="Adresa ta de email"
              className="flex-1 px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-white focus:outline-none"
            />
            <button
              className="bg-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200"
              style={{ color: '#b04b0f' }}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f9f6eb'}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'white'}
              Abonează-te
            </button>
          </div>
        </div>
      </section>
    </Layout>
  );
}
