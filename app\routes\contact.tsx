import type { Route } from "./+types/contact";
import { Layout } from "../components/Layout";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Contact - Educația Holistică" },
    { name: "description", content: "Contactează Asociația Educația Holistică pentru informații despre programele noastre educaționale." },
  ];
}

export default function Contact() {
  return (
    <Layout>
      {/* Hero Section */}
      <section className="py-20" style={{ background: 'linear-gradient(135deg, #fef7f0 0%, #f9f6eb 50%, #fdeee0 100%)' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6 font-playfair">
              Contactează-<span style={{ color: '#b04b0f' }}>ne</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Suntem aici să răspundem la întrebările tale și să te ghidăm în 
              călătoria către o educație mai holistică și echilibrată.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
            {/* Contact Form */}
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6 font-playfair">
                Trimite-ne un Mesaj
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                Completează formularul de mai jos și îți vom răspunde în cel mai scurt timp posibil.
              </p>
              
              <form className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="nume" className="block text-sm font-medium text-gray-700 mb-2">
                      Nume *
                    </label>
                    <input
                      type="text"
                      id="nume"
                      name="nume"
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                      placeholder="Numele tău"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="prenume" className="block text-sm font-medium text-gray-700 mb-2">
                      Prenume *
                    </label>
                    <input
                      type="text"
                      id="prenume"
                      name="prenume"
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                      placeholder="Prenumele tău"
                    />
                  </div>
                </div>
                
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                    placeholder="<EMAIL>"
                  />
                </div>
                
                <div>
                  <label htmlFor="telefon" className="block text-sm font-medium text-gray-700 mb-2">
                    Telefon
                  </label>
                  <input
                    type="tel"
                    id="telefon"
                    name="telefon"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                    placeholder="+40 123 456 789"
                  />
                </div>
                
                <div>
                  <label htmlFor="subiect" className="block text-sm font-medium text-gray-700 mb-2">
                    Subiect *
                  </label>
                  <select
                    id="subiect"
                    name="subiect"
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                  >
                    <option value="">Selectează un subiect</option>
                    <option value="programe-copii">Programe pentru Copii</option>
                    <option value="workshop-adulti">Workshop-uri pentru Adulți</option>
                    <option value="formare-educatori">Formare pentru Educatori</option>
                    <option value="evenimente">Evenimente</option>
                    <option value="colaborare">Oportunități de Colaborare</option>
                    <option value="altele">Altele</option>
                  </select>
                </div>
                
                <div>
                  <label htmlFor="mesaj" className="block text-sm font-medium text-gray-700 mb-2">
                    Mesaj *
                  </label>
                  <textarea
                    id="mesaj"
                    name="mesaj"
                    rows={6}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                    placeholder="Scrie-ne mesajul tău aici..."
                  ></textarea>
                </div>
                
                <div className="flex items-start">
                  <input
                    type="checkbox"
                    id="gdpr"
                    name="gdpr"
                    required
                    className="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <label htmlFor="gdpr" className="ml-3 text-sm text-gray-600">
                    Sunt de acord cu prelucrarea datelor personale în conformitate cu 
                    <a href="#" className="text-indigo-600 hover:text-indigo-700"> Politica de Confidențialitate</a>.
                  </label>
                </div>
                
                <button
                  type="submit"
                  className="w-full text-white py-3 px-6 rounded-lg font-semibold transition-colors duration-200"
                  style={{ backgroundColor: '#b04b0f' }}
                  onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#a0420e'}
                  onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#b04b0f'}
                >
                  Trimite Mesajul
                </button>
              </form>
            </div>

            {/* Contact Info */}
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6 font-playfair">
                Informații de Contact
              </h2>
              
              <div className="space-y-8">
                <div className="flex items-start space-x-4">
                  <div className="p-3 rounded-full" style={{ backgroundColor: '#fef7f0' }}>
                    <svg className="h-6 w-6" style={{ color: '#b04b0f' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Adresa</h3>
                    <p className="text-gray-600">
                      Strada Educației Holistice, Nr. 123<br />
                      Sector 1, București 010101<br />
                      România
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4">
                  <div className="bg-green-100 p-3 rounded-full">
                    <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Telefon</h3>
                    <p className="text-gray-600">
                      <a href="tel:+40123456789" className="hover:text-indigo-600 transition-colors duration-200">
                        +40 123 456 789
                      </a>
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      Luni - Vineri: 09:00 - 17:00
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4">
                  <div className="bg-blue-100 p-3 rounded-full">
                    <svg className="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Email</h3>
                    <p className="text-gray-600">
                      <a href="mailto:<EMAIL>" className="hover:text-indigo-600 transition-colors duration-200">
                        <EMAIL>
                      </a>
                    </p>
                    <p className="text-gray-600 mt-1">
                      <a href="mailto:<EMAIL>" className="hover:text-indigo-600 transition-colors duration-200">
                        <EMAIL>
                      </a>
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4">
                  <div className="bg-purple-100 p-3 rounded-full">
                    <svg className="h-6 w-6 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Social Media</h3>
                    <div className="space-y-1">
                      <p className="text-gray-600">
                        <a href="https://facebook.com/educatia-holistica" className="hover:text-indigo-600 transition-colors duration-200">
                          Facebook: @educatia-holistica
                        </a>
                      </p>
                      <p className="text-gray-600">
                        <a href="https://instagram.com/educatia.holistica" className="hover:text-indigo-600 transition-colors duration-200">
                          Instagram: @educatia.holistica
                        </a>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Map Placeholder */}
              <div className="mt-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Locația Noastră</h3>
                <div className="bg-gray-200 rounded-lg h-64 flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <svg className="h-12 w-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <p>Hartă interactivă</p>
                    <p className="text-sm">Google Maps va fi integrată aici</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
}
