import type { Route } from "./+types/home";
import { Layout } from "../components/Layout";
import { Link } from "react-router";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Educația Holistică - Asociație Non-Profit" },
    { name: "description", content: "Asociația Educația Holistică promovează o educație completă care dezvoltă armonios toate aspectele personalității umane." },
  ];
}

export default function Home() {
  return (
    <Layout>
      {/* Hero Section */}
      <section className="relative py-20" style={{ background: 'linear-gradient(135deg, #fef7f0 0%, #f9f6eb 50%, #fdeee0 100%)' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6 font-playfair">
                Educația <span style={{ color: '#b04b0f' }}>Holistică</span>
              </h1>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Dezvoltăm armonios toate aspectele personalității umane prin programe
                educaționale inovatoare care integrează dimensiunea intelectuală,
                emoțională, socială și spirituală.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/programe"
                  className="text-white px-8 py-3 rounded-lg font-semibold transition-colors duration-200 text-center"
                  style={{ backgroundColor: '#b04b0f' }}
                  onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#a0420e'}
                  onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#b04b0f'}
                >
                  Descoperă Programele
                </Link>
                <Link
                  to="/despre"
                  className="border-2 px-8 py-3 rounded-lg font-semibold transition-colors duration-200 text-center"
                  style={{ borderColor: '#b04b0f', color: '#b04b0f' }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#fef7f0';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  Află Mai Multe
                </Link>
              </div>
            </div>
            <div className="relative">
              <img
                src="https://via.placeholder.com/600x400/fef7f0/b04b0f?text=Educatie+Holistica"
                alt="Educație Holistică"
                className="rounded-2xl shadow-2xl"
              />
              <div className="absolute -bottom-6 -left-6 bg-white p-6 rounded-xl shadow-lg">
                <div className="flex items-center space-x-4">
                  <div className="p-3 rounded-full" style={{ backgroundColor: '#fef7f0' }}>
                    <svg className="h-6 w-6" style={{ color: '#b04b0f' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  </div>
                  <div>
                    <p className="font-semibold" style={{ color: '#b04b0f' }}>500+</p>
                    <p className="text-sm text-gray-600">Beneficiari</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 font-playfair">
              Principiile Noastre
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Credem într-o educație care nu se limitează la transmiterea de cunoștințe,
              ci care formează oameni completi, echilibrați și conștienți.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center p-6">
              <div className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4" style={{ backgroundColor: '#fef7f0' }}>
                <svg className="h-8 w-8" style={{ color: '#b04b0f' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Dezvoltare Intelectuală</h3>
              <p className="text-gray-600">
                Stimulăm gândirea critică, creativitatea și dorința de învățare continuă.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4" style={{ backgroundColor: '#fdeee0' }}>
                <svg className="h-8 w-8" style={{ color: '#a0420e' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Inteligență Emotională</h3>
              <p className="text-gray-600">
                Dezvoltăm capacitatea de a înțelege și gestiona emoțiile proprii și ale altora.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4" style={{ backgroundColor: '#fad9c1' }}>
                <svg className="h-8 w-8" style={{ color: '#8a380c' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Competențe Sociale</h3>
              <p className="text-gray-600">
                Promovăm colaborarea, empatia și responsabilitatea socială.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4" style={{ backgroundColor: '#f6be97' }}>
                <svg className="h-8 w-8" style={{ color: '#72300a' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Dezvoltare Spirituală</h3>
              <p className="text-gray-600">
                Cultivăm sensul de purpose, valorile și conexiunea cu sine și cu lumea.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Programs Preview */}
      <section className="py-20" style={{ backgroundColor: '#f9f6eb' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 font-playfair">
              Programele Noastre
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Oferim o gamă variată de programe educaționale adaptate diferitelor vârste și nevoi.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
              <img
                src="https://via.placeholder.com/400x250/F3F4F6/6B7280?text=Educatie+Copii"
                alt="Educație pentru Copii"
                className="w-full h-48 object-cover"
              />
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Educație pentru Copii</h3>
                <p className="text-gray-600 mb-4">
                  Programe creative și interactive pentru dezvoltarea armonioasă a copiilor.
                </p>
                <Link
                  to="/programe"
                  className="font-semibold transition-colors duration-200"
                  style={{ color: '#b04b0f' }}
                  onMouseEnter={(e) => e.currentTarget.style.color = '#a0420e'}
                  onMouseLeave={(e) => e.currentTarget.style.color = '#b04b0f'}
                >
                  Află mai multe →
                </Link>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
              <img
                src="https://via.placeholder.com/400x250/F3F4F6/6B7280?text=Workshop+Adulti"
                alt="Workshop-uri pentru Adulți"
                className="w-full h-48 object-cover"
              />
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Workshop-uri pentru Adulți</h3>
                <p className="text-gray-600 mb-4">
                  Sesiuni de dezvoltare personală și profesională pentru adulți.
                </p>
                <Link
                  to="/programe"
                  className="font-semibold transition-colors duration-200"
                  style={{ color: '#b04b0f' }}
                  onMouseEnter={(e) => e.currentTarget.style.color = '#a0420e'}
                  onMouseLeave={(e) => e.currentTarget.style.color = '#b04b0f'}
                >
                  Află mai multe →
                </Link>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
              <img
                src="https://via.placeholder.com/400x250/F3F4F6/6B7280?text=Formare+Educatori"
                alt="Formare pentru Educatori"
                className="w-full h-48 object-cover"
              />
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Formare pentru Educatori</h3>
                <p className="text-gray-600 mb-4">
                  Cursuri specializate pentru profesorii și educatorii interesați de abordarea holistică.
                </p>
                <Link
                  to="/programe"
                  className="font-semibold transition-colors duration-200"
                  style={{ color: '#b04b0f' }}
                  onMouseEnter={(e) => e.currentTarget.style.color = '#a0420e'}
                  onMouseLeave={(e) => e.currentTarget.style.color = '#b04b0f'}
                >
                  Află mai multe →
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20" style={{ backgroundColor: '#b04b0f' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4 font-playfair">
            Alătură-te Comunității Noastre
          </h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto" style={{ color: '#fef7f0' }}>
            Fii parte din mișcarea pentru o educație mai umană și mai completă.
            Împreună putem transforma viitorul educației.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/contact"
              className="bg-white px-8 py-3 rounded-lg font-semibold transition-colors duration-200"
              style={{ color: '#b04b0f' }}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f9f6eb'}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'white'}
            >
              Contactează-ne
            </Link>
            <Link
              to="/evenimente"
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold transition-colors duration-200"
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'white';
                e.currentTarget.style.color = '#b04b0f';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = 'white';
              }}
            >
              Vezi Evenimente
            </Link>
          </div>
        </div>
      </section>
    </Layout>
  );
}
